
<?php $__env->startSection('js'); ?>
<script src="https://cdn.paddle.com/paddle/paddle.js"></script>
<script>
    "use strict";
    function showPlugins(filterPlugins){
        $('.plugin-all').hide();
        var toShow='.plugin-'+filterPlugins;
        $(toShow).show();
    }
</script>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="header bg-gradient-primary pb-8 pt-5 pt-md-8">
</div>
<div class="container-fluid mt--9">
    <div class="row">
        <div class="col-xl-12 order-xl-1">
            <div class="card bg-secondary shadow">
                <div class="card-header bg-white border-0">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h3 class="mb-0"><?php echo e(__('Apps')); ?></h3>
                            
                        </div>
                        <?php if(config('settings.is_demo') | config('settings.is_demo')): ?> 
                        <div class="col-6 text-right">
                            <a  onclick="alert('Disabled in demo')" class="btn btn-success text-white"><?php echo e(__('Upload plugin')); ?></a>
                        </div>
                        <?php else: ?>
                            <div class="col-6 text-right">
                                <a  onclick="$('#appupload').click();" class="btn btn-success text-white"><?php echo e(__('Upload plugin')); ?></a>
                            </div>
                        <?php endif; ?>
                        
                    </div>
                </div>
                <div class="card-body">
                    
                   
                    
                    <?php echo $__env->make('partials.flash', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <form method="post" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div style="display: none">
                            <input name="appupload" type="file" class="" id="appupload" accept=".zip,.rar,.7zip"   onchange="form.submit()">
                        </div>
                    </form>

                    <div class="row">
                        <?php if(empty($apps)): ?>
                        <p>
                            <?php echo e(__("There are no apps at the moment")); ?>

                        </p>
                        <?php endif; ?>
                        <?php $__currentLoopData = $apps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $app): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-xl-3 col-lg-6 col-md-6 col-sm-6 mt-3 plugin-all <?php if($app->installed): ?> <?php echo e('plugin-installed'); ?> <?php endif; ?> <?php  foreach ($app->category as $cat){echo "plugin-".$cat." ";} ?>">
                            <div class="card" style="width: 18rem;">
                                <img class="card-img-top" src="<?php echo e($app->image); ?>" alt="<?php echo e($app->name); ?>">
                                <div class="card-body">
                                <h5 class="card-title"><?php echo e($app->name); ?> - <?php echo e($app->price); ?> <?php if($app->installed): ?><span class="small text-green"><?php echo e(__('installed')); ?> v<?php echo e($app->version); ?></span><?php endif; ?></h5> <?php if($app->installed&&!$app->updateAvailable): ?> <span class="badge badge-success"><?php echo e(__('Latest version')); ?></span> <?php endif; ?>
                                <p class="card-text"><?php echo e($app->description); ?></p>
                                
                                <?php if($app->installed): ?>
                                    <!-- APP IS IN LOCAL SYSTEM -->
                                    <a href="<?php echo e(route('admin.settings.index')); ?>" class="mt-1 btn btn-sm btn-outline-success"><span class="btn-inner--icon"><i class="fas fa-cog"></i></span>   <?php echo e(__('Settings')); ?></a>
                                    <?php if($app->updateAvailable&&isset($app->file)): ?>
                                        <?php if(!config('settings.is_demo',false)): ?>
                                            <a target="_blank" href="<?php echo e($app->file); ?>"  class="mt-1 btn btn-sm btn-outline-primary"><span class="btn-inner--icon"><i class="fas fa-refresh"></i></span> ⬇️  <?php echo e(__('New update')); ?> v<?php echo e($app->latestVersion); ?></a>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if($app->updateAvailable&&isset($app->link)): ?>
                                        <?php if(!config('settings.is_demo',false)): ?>
                                            <a target="_blank" href="<?php echo e($app->link); ?>" class="mt-1 btn btn-sm btn-outline-primary"><span class="btn-inner--icon"><i class="fas fa-refresh"></i></span> ⬇️  <?php echo e(__('New update')); ?> v<?php echo e($app->latestVersion); ?></a>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    
                                    <!-- <?php if($app->price=="Free"): ?>
                                        <p class="card-text mt-2"><?php echo e(__('Price')); ?>: 0$</p>
                                    <?php else: ?>
                                        <p class="card-text mt-2"><?php echo e(__('Price')); ?>: <?php echo e($app->price); ?></p>
                                    <?php endif; ?> -->
                                <?php else: ?>
                                
                                    <!-- APP IS NOT IN LOCAL SYSTEM -->
                                    
                                    <?php if(isset($app->file)): ?>
                                        <?php if(!config('settings.is_demo',false)): ?>
                                            <!-- WE HAVE Access to the file. This is all access pass -->
                                            <a href="<?php echo e($app->file); ?>" target="_blank"  class="mt-1 btn btn-sm btn-outline-primary"><span class="btn-inner--icon"><i class="fas fa-download"></i></span>   <?php echo e(__('Download')); ?></a>  
                                        <?php endif; ?>
                                        
                                    <?php else: ?>
                                        <!-- WE don't HAVE Access to the file.  -->
                                        <?php if(isset($app->paddle)): ?>
                                            <?php if(isset($app->discount_code)): ?>
                                                <a  onclick="openPaddleDiscount('<?php echo e($app->paddle); ?>','<?php echo e($app->discount_code); ?>')" class="mt-1 btn btn-sm btn-outline-primary"><i class="fas fa-shopping-cart"></i></span>  <?php echo e(__('Buy with discount')." - ".$app->price); ?></a>
                                            <?php else: ?>
                                                <a  onclick="openPaddle(<?php echo e($app->paddle); ?>)" class="mt-1 btn btn-sm btn-outline-primary"><span class="btn-inner--icon"><i class="fas fa-shopping-cart"></i></span>  <?php echo e(__('Buy now')." - ".$app->price); ?></a>  
                                            <?php endif; ?>
                                        <?php elseif(isset($app->link)&&strlen($app->link)>3): ?>
                                            <!-- Outside link like CodeCanyon -->
                                            <a target="_blank" href="<?php echo e($app->link); ?>" class="mt-1 btn btn-sm btn-outline-primary"><span class="btn-inner--icon"><i class="fas fa-shopping-cart"></i></span> <?php echo e(__('Buy now')." - ".$app->price); ?></a>
                                        <?php endif; ?>


                                        <?php if(isset($app->ppfolder)): ?>
                                            <a target="_blank"  href="<?php echo e($app->ppfolder); ?>" class="mt-1 btn btn-sm btn-outline-primary"><span class="btn-inner--icon"><i class="fas fa-download"></i></span>  <?php echo e(__('Download update')); ?></a>  
                                        <?php endif; ?>

                                        
                                        
                                    <?php endif; ?>
                               
                                <?php endif; ?>

                                <?php if(isset($app->docs)): ?>
                                    <a href="<?php echo e($app->docs); ?>" target="_blank" class="mt-1 btn btn-sm btn-outline-info"><span class="btn-inner--icon"><i class="fas fa-file-pdf"></i></span> <?php echo e(__('Docs')); ?></a>
                                <?php endif; ?>

                                <?php if(isset($app->video)): ?>
                                    <a href="<?php echo e($app->video); ?>" target="_blank" class="mt-1 btn btn-sm btn-outline-danger"><span class="btn-inner--icon"><i class="fas fa-video"></i></span> <?php echo e(__('Video')); ?></a>
                                <?php endif; ?>

                                <?php if(isset($app->demo)): ?>
                                    <a href="<?php echo e($app->demo); ?>" target="_blank" class="mt-1 btn btn-sm btn-outline-primary"><span class="btn-inner--icon"><i class="fas fa-eye"></i></span> <?php echo e(__('Demo')); ?></a>
                                <?php endif; ?>
                                
                                </div>
                            </div>  
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', ['title' => __('Settings'), 'hideActions'=>true], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\zaptra\resources\views/apps/index.blade.php ENDPATH**/ ?>